* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
}

/* Header & Navigation */
header {
    background-color: #2c3e50;
    color: #fff;
    padding: 1rem 0;
}

nav {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
}

.logo h1 {
    font-size: 1.5rem;
}

.navbar {
    display: flex;
    list-style: none;
}

.navbar li {
    margin-left: 20px;
}

.navbar a {
    color: #fff;
    text-decoration: none;
    padding: 5px 10px;
    border-radius: 4px;
    transition: background-color 0.3s;
}

.navbar a:hover, .navbar a.active {
    background-color: #3498db;
}

/* Main Content */
main {
    min-height: calc(100vh - 130px);
}

/* Hero Section */
.hero {
    background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url('https://via.placeholder.com/1920x600') no-repeat center center/cover;
    color: #fff;
    text-align: center;
    padding: 100px 20px;
}

.hero-content h1 {
    font-size: 2.5rem;
    margin-bottom: 20px;
}

.hero-content p {
    font-size: 1.2rem;
    margin-bottom: 30px;
}

.btn {
    display: inline-block;
    background-color: #3498db;
    color: #fff;
    text-decoration: none;
    padding: 10px 20px;
    border-radius: 5px;
    transition: background-color 0.3s;
}

.btn:hover {
    background-color: #2980b9;
}

.btn-small {
    display: inline-block;
    background-color: #3498db;
    color: #fff;
    text-decoration: none;
    padding: 5px 10px;
    border-radius: 3px;
    font-size: 0.9rem;
    transition: background-color 0.3s;
}

.btn-small:hover {
    background-color: #2980b9;
}

/* Features Section */
.features {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    max-width: 1200px;
    margin: 50px auto;
    padding: 0 20px;
}

.feature-box {
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    padding: 20px;
    width: calc(33.333% - 20px);
    margin-bottom: 30px;
}

.feature-box h3 {
    color: #2c3e50;
    margin-bottom: 10px;
}

/* Page Header */
.page-header {
    background-color: #3498db;
    color: #fff;
    text-align: center;
    padding: 50px 20px;
}

.page-header h1 {
    font-size: 2rem;
}

/* Content Section */
.content-section {
    max-width: 1200px;
    margin: 50px auto;
    padding: 0 20px;
}

/* Profile Page */
.profile-content {
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
}

.profile-image {
    flex: 1;
    min-width: 300px;
}

.profile-image img {
    width: 100%;
    border-radius: 5px;
}

.profile-text {
    flex: 2;
    min-width: 300px;
}

.profile-text h2 {
    color: #2c3e50;
    margin: 20px 0 10px;
}

.profile-text p {
    margin-bottom: 15px;
}

.profile-text ul {
    margin-left: 20px;
    margin-bottom: 15px;
}

/* Department Page */
.department-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
}

.department-card {
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    padding: 20px;
}

.department-card h3 {
    color: #2c3e50;
    margin-bottom: 10px;
}

.department-card p {
    margin-bottom: 15px;
}

/* Contact Page */
.contact-container {
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
}

.contact-info, .contact-form {
    flex: 1;
    min-width: 300px;
}

.contact-info h2, .contact-form h2 {
    color: #2c3e50;
    margin-bottom: 20px;
}

.contact-info p {
    margin-bottom: 10px;
}

.social-media {
    margin-top: 20px;
}

.social-icons {
    display: flex;
    gap: 10px;
    margin-top: 10px;
}

.social-icon {
    background-color: #3498db;
    color: #fff;
    text-decoration: none;
    padding: 5px 10px;
    border-radius: 3px;
    transition: background-color 0.3s;
}

.social-icon:hover {
    background-color: #2980b9;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
}

.form-group input, .form-group textarea {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

/* Footer */
footer {
    background-color: #2c3e50;
    color: #fff;
    text-align: center;
    padding: 20px;
}

/* Responsive Design */
@media (max-width: 768px) {
    nav {
        flex-direction: column;
    }
    
    .navbar {
        margin-top: 15px;
    }
    
    .navbar li {
        margin: 0 10px;
    }
    
    .feature-box {
        width: 100%;
    }
}
