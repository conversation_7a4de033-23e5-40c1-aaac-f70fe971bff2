@echo off
title AYAM GORENG JOSS - Restaurant Management System
color 0A

echo.
echo ========================================
echo    AYAM GORENG JOSS
echo    Restaurant Management System
echo ========================================
echo.
echo Pilih opsi:
echo.
echo 1. Setup Database (Pertama kali)
echo 2. Jalankan Server
echo 3. Buka Admin Panel
echo 4. Buka Website
echo 5. Keluar
echo.
set /p choice="Masukkan pilihan (1-5): "

if "%choice%"=="1" goto setup
if "%choice%"=="2" goto server
if "%choice%"=="3" goto admin
if "%choice%"=="4" goto website
if "%choice%"=="5" goto exit

:setup
echo.
echo ========================================
echo    SETUP DATABASE
echo ========================================
echo.
echo Pastikan XAMPP MySQL sudah running!
echo.
pause
echo.
echo Generating application key...
php artisan key:generate
echo.
echo Creating storage link...
php artisan storage:link
echo.
echo Running migrations...
php artisan migrate:fresh
echo.
echo Seeding database...
php artisan db:seed
echo.
echo Clearing cache...
php artisan cache:clear
php artisan config:clear
echo.
echo ========================================
echo    SETUP SELESAI!
echo ========================================
echo.
echo Akun Admin:
echo - <EMAIL> / password
echo - <EMAIL> / password
echo - <EMAIL> / password
echo.
echo Akun Customer:
echo - <EMAIL> / password123
echo.
pause
goto menu

:server
echo.
echo ========================================
echo    STARTING SERVER
echo ========================================
echo.
echo Server akan berjalan di:
echo - Website: http://localhost:8000
echo - Admin: http://localhost:8000/admin/login
echo.
echo Tekan Ctrl+C untuk stop server
echo ========================================
echo.
php artisan serve --host=0.0.0.0 --port=8000
goto menu

:admin
echo.
echo Membuka Admin Panel...
start http://localhost:8000/admin/login
goto menu

:website
echo.
echo Membuka Website...
start http://localhost:8000
goto menu

:exit
echo.
echo Terima kasih telah menggunakan Ayam Goreng Joss!
echo.
pause
exit

:menu
echo.
echo Kembali ke menu utama...
timeout /t 2 >nul
cls
goto start
