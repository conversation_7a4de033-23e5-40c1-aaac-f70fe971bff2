<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\Category;
use App\Models\Customer;
use App\Models\Menu;
use App\Models\Kategori;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;

class FrontController extends Controller
{
    /**
     * Display the home page.
     */
    public function home()
    {
        // Use new models first, fallback to old ones for compatibility
        $categories = Category::active()->get();
        if ($categories->isEmpty()) {
            $categories = Kategori::all();
        }

        $products = Product::active()->with('category')->take(8)->get();
        if ($products->isEmpty()) {
            $products = Menu::take(6)->get();
        }

        return view('home', [
            'kategoris' => $categories,
            'menus' => $products,
            'categories' => $categories,
            'products' => $products
        ]);
    }

    /**
     * Display the menu page.
     */
    public function menu(Request $request)
    {
        // Use new models first, fallback to old ones for compatibility
        $categories = Category::active()->get();
        if ($categories->isEmpty()) {
            $categories = Kategori::all();
        }

        $query = Product::active()->with('category');

        // Category filter
        if ($request->has('category') && $request->category) {
            $query->where('category_id', $request->category);
        }

        // Search filter
        if ($request->has('search') && $request->search) {
            $query->where('name', 'like', '%' . $request->search . '%');
        }

        // Spice level filter
        if ($request->has('spice_level') && $request->spice_level) {
            $query->where('spice_level', $request->spice_level);
        }

        // Price range filter
        if ($request->has('price_min') && $request->price_min) {
            $query->where('price', '>=', $request->price_min);
        }

        if ($request->has('price_max') && $request->price_max) {
            $query->where('price', '<=', $request->price_max);
        }

        $products = $query->orderBy('name')->paginate(12);

        // Fallback to old menu system if no products
        if ($products->isEmpty()) {
            $products = Menu::paginate(12);
        }

        return view('menu', [
            'kategoris' => $categories,
            'menus' => $products,
            'categories' => $categories,
            'products' => $products
        ]);
    }

    /**
     * Display the contact page.
     */
    public function kontak()
    {
        $categories = Category::active()->get();
        if ($categories->isEmpty()) {
            $categories = Kategori::all();
        }

        return view('kontak', [
            'kategoris' => $categories,
            'categories' => $categories
        ]);
    }

    /**
     * Display product detail.
     */
    public function productDetail($id)
    {
        $product = Product::with(['category', 'reviews.customer'])->findOrFail($id);
        
        // Related products from same category
        $relatedProducts = Product::where('category_id', $product->category_id)
            ->where('id', '!=', $product->id)
            ->where('is_active', true)
            ->take(4)
            ->get();

        $categories = Category::active()->get();

        return view('product-detail', compact('product', 'relatedProducts', 'categories'));
    }

    /**
     * Search products (AJAX).
     */
    public function searchProducts(Request $request)
    {
        $query = $request->get('q');
        
        $products = Product::where('name', 'like', '%' . $query . '%')
            ->where('is_active', true)
            ->with('category')
            ->take(10)
            ->get();

        return response()->json([
            'products' => $products->map(function($product) {
                return [
                    'id' => $product->id,
                    'name' => $product->name,
                    'price' => $product->formatted_price,
                    'image' => $product->image ? asset('storage/' . $product->image) : asset('images/no-image.jpg'),
                    'category' => $product->category->name ?? '',
                    'spice_level' => $product->spice_level,
                    'url' => route('product.detail', $product->id)
                ];
            })
        ]);
    }

    /**
     * Get products by category (AJAX).
     */
    public function getProductsByCategory($categoryId)
    {
        $products = Product::where('category_id', $categoryId)
            ->where('is_active', true)
            ->with('category')
            ->get();

        return response()->json([
            'products' => $products->map(function($product) {
                return [
                    'id' => $product->id,
                    'name' => $product->name,
                    'description' => $product->description,
                    'price' => $product->formatted_price,
                    'image' => $product->image ? asset('storage/' . $product->image) : asset('images/no-image.jpg'),
                    'spice_level' => $product->spice_level,
                    'spice_level_text' => $product->spice_level_text,
                    'is_premium' => $product->is_premium
                ];
            })
        ]);
    }

    // Legacy methods for backward compatibility
    public function index()
    {
        return $this->menu();
    }

    public function register()
    {
        return redirect()->route('customer.register');
    }

    public function login()
    {
        return redirect()->route('customer.login');
    }

    public function postlogin(Request $request)
    {
        return redirect()->route('customer.login')->with('info', 'Silakan gunakan form login yang baru.');
    }

    public function logout()
    {
        return redirect()->route('customer.logout');
    }

    public function create()
    {
        return redirect()->route('customer.register');
    }

    public function store(Request $request)
    {
        return redirect()->route('customer.register')->with('info', 'Silakan gunakan form registrasi yang baru.');
    }

    public function show($id)
    {
        return $this->productDetail($id);
    }

    public function edit($id)
    {
        return redirect('/');
    }

    public function update(Request $request, $id)
    {
        return redirect('/');
    }

    public function destroy($id)
    {
        return redirect('/');
    }
}
