<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $user=[
            [
                'name'=>'admin',
                'email'=>'<EMAIL>',
                'password'=>bcrypt('123'),
                'level'=>'admin',
            ],
            [
                'name'=>'kasir',
                'email'=>'<EMAIL>',
                'password'=>bcrypt('123'),
                'level'=>'kasir',
            ],
            [
                'name'=>'manager',
                'email'=>'<EMAIL>',
                'password'=>bcrypt('123'),
                'level'=>'manager',
            ],
        ];

        foreach ($user as $key => $value) {
            User::create($value);
        }
    }
}
