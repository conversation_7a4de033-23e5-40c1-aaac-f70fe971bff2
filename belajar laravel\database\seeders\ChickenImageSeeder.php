<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use App\Models\Product;

class ChickenImageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Array of chicken images from various sources
        $chickenImages = [
            'ayam-joss-original.jpg' => 'https://images.unsplash.com/photo-1562967914-608f82629710?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
            'ayam-joss-pedas-manis.jpg' => 'https://images.unsplash.com/photo-1569058242253-92a9c755a0ec?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
            'ayam-joss-extreme.jpg' => 'https://images.unsplash.com/photo-1626645738196-c2a7c87a8f58?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
            'ayam-joss-keju.jpg' => 'https://images.unsplash.com/photo-1598103442097-8b74394b95c6?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
            'ayam-bumbu-bali.jpg' => 'https://images.unsplash.com/photo-1604503468506-a8da13d82791?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
            'ayam-kremes.jpg' => 'https://images.unsplash.com/photo-1567620832903-9fc6debc209f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
            'ayam-sambal-matah.jpg' => 'https://images.unsplash.com/photo-1606491956689-2ea866880c84?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
            'ayam-black-pepper.jpg' => 'https://images.unsplash.com/photo-1594221708779-94832f4320d1?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
        ];

        $sideImages = [
            'nasi-putih.jpg' => 'https://images.unsplash.com/photo-1586201375761-83865001e31c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
            'french-fries.jpg' => 'https://images.unsplash.com/photo-1573080496219-bb080dd4f877?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
            'lalapan.jpg' => 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
            'tahu-tempe.jpg' => 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
        ];

        $drinkImages = [
            'es-teh-manis.jpg' => 'https://images.unsplash.com/photo-1556679343-c7306c1976bc?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
            'es-jeruk-nipis.jpg' => 'https://images.unsplash.com/photo-1621263764928-df1444c5e859?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
            'es-kelapa-muda.jpg' => 'https://images.unsplash.com/photo-1559181567-c3190ca9959b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
            'jus-alpukat.jpg' => 'https://images.unsplash.com/photo-1623065422902-30a2d299bbe4?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
        ];

        $allImages = array_merge($chickenImages, $sideImages, $drinkImages);

        // Create products directory if it doesn't exist
        if (!Storage::disk('public')->exists('products')) {
            Storage::disk('public')->makeDirectory('products');
        }

        // Download and save images
        foreach ($allImages as $filename => $url) {
            try {
                $this->command->info("Downloading {$filename}...");
                
                // Download image
                $response = Http::timeout(30)->get($url);
                
                if ($response->successful()) {
                    // Save to storage
                    Storage::disk('public')->put('products/' . $filename, $response->body());
                    $this->command->info("✓ Downloaded {$filename}");
                } else {
                    $this->command->warn("✗ Failed to download {$filename}");
                }
            } catch (\Exception $e) {
                $this->command->warn("✗ Error downloading {$filename}: " . $e->getMessage());
            }
        }

        // Create placeholder images for missing ones
        $this->createPlaceholderImages();

        $this->command->info('Image seeding completed!');
    }

    /**
     * Create placeholder images for products that don't have images.
     */
    private function createPlaceholderImages()
    {
        $placeholderImages = [
            'ayam-joss-original.jpg',
            'ayam-joss-pedas-manis.jpg', 
            'ayam-joss-extreme.jpg',
            'ayam-joss-keju.jpg',
            'ayam-bumbu-bali.jpg',
            'ayam-kremes.jpg',
            'ayam-sambal-matah.jpg',
            'ayam-black-pepper.jpg',
            'nasi-putih.jpg',
            'french-fries.jpg',
            'lalapan.jpg',
            'tahu-tempe.jpg',
            'es-teh-manis.jpg',
            'es-jeruk-nipis.jpg',
            'es-kelapa-muda.jpg',
            'jus-alpukat.jpg',
        ];

        foreach ($placeholderImages as $filename) {
            if (!Storage::disk('public')->exists('products/' . $filename)) {
                // Create a simple placeholder image
                $this->createSimplePlaceholder($filename);
            }
        }
    }

    /**
     * Create a simple placeholder image.
     */
    private function createSimplePlaceholder($filename)
    {
        // Create a simple 400x300 placeholder image
        $width = 400;
        $height = 300;
        
        $image = imagecreate($width, $height);
        
        // Colors
        $bg_color = imagecolorallocate($image, 240, 240, 240);
        $text_color = imagecolorallocate($image, 100, 100, 100);
        
        // Fill background
        imagefill($image, 0, 0, $bg_color);
        
        // Add text
        $text = pathinfo($filename, PATHINFO_FILENAME);
        $text = str_replace('-', ' ', $text);
        $text = ucwords($text);
        
        // Calculate text position
        $font_size = 3;
        $text_width = imagefontwidth($font_size) * strlen($text);
        $text_height = imagefontheight($font_size);
        $x = ($width - $text_width) / 2;
        $y = ($height - $text_height) / 2;
        
        imagestring($image, $font_size, $x, $y, $text, $text_color);
        
        // Save as JPEG
        ob_start();
        imagejpeg($image, null, 80);
        $imageData = ob_get_contents();
        ob_end_clean();
        
        Storage::disk('public')->put('products/' . $filename, $imageData);
        
        imagedestroy($image);
        
        $this->command->info("✓ Created placeholder for {$filename}");
    }
}
