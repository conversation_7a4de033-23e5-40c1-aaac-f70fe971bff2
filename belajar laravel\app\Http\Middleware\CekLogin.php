<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CekLogin
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @param  string|null  $roles
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next, $roles = null)
    {
        if (!Auth::check()) {
            return redirect()->route('admin.login')->with('error', 'Silakan login terlebih dahulu');
        }
        
        $user = Auth::user();
        
        // If no specific role required, just check if authenticated
        if (!$roles) {
            return $next($request);
        }
        
        // Check if user has required role
        $allowedRoles = explode('|', $roles);
        if (in_array($user->level, $allowedRoles)) {
            return $next($request);
        }
        
        return redirect()->route('admin.login')->with('error', 'Anda tidak memiliki akses ke halaman ini');
    }
}
