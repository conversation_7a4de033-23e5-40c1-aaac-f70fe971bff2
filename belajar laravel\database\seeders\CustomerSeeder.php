<?php

namespace Database\Seeders;

use App\Models\Customer;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class CustomerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $customers = [
            [
                'name' => '<PERSON><PERSON>',
                'email' => '<EMAIL>',
                'phone' => '081234567890',
                'address' => 'Jl. Merdeka No. 123, Jakarta Selatan',
                'password' => Hash::make('password123'),
            ],
            [
                'name' => '<PERSON>i <PERSON>hayu',
                'email' => '<EMAIL>',
                'phone' => '082345678901',
                'address' => 'Jl. Pahlawan No. 45, Jakarta Pusat',
                'password' => Hash::make('password123'),
            ],
            [
                'name' => 'Agus Wijaya',
                'email' => '<EMAIL>',
                'phone' => '083456789012',
                'address' => 'Jl. Sudirman No. 78, Jakarta Barat',
                'password' => Hash::make('password123'),
            ],
            [
                'name' => 'Dewi Les<PERSON>i',
                'email' => '<EMAIL>',
                'phone' => '084567890123',
                'address' => 'Jl. Gatot Subroto No. 56, Jakarta Selatan',
                'password' => Hash::make('password123'),
            ],
            [
                'name' => 'Rudi Hartono',
                'email' => '<EMAIL>',
                'phone' => '085678901234',
                'address' => 'Jl. Thamrin No. 34, Jakarta Pusat',
                'password' => Hash::make('password123'),
            ],
            [
                'name' => 'Rina Susanti',
                'email' => '<EMAIL>',
                'phone' => '086789012345',
                'address' => 'Jl. Kebon Jeruk No. 12, Jakarta Barat',
                'password' => Hash::make('password123'),
            ],
        ];

        foreach ($customers as $customer) {
            Customer::create($customer);
        }
    }
}
