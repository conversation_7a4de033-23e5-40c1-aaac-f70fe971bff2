<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Order extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'orders_new';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'customer_id',
        'order_number',
        'order_date',
        'total_amount',
        'status',
        'payment_method',
        'payment_status',
        'promo_id',
        'discount_amount',
        'delivery_address',
        'delivery_fee',
        'notes',
        // Keep original fields for backward compatibility
        'idorder',
        'idpelanggan',
        'tglorder',
        'total',
        'bayar',
        'kembali',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'order_date' => 'datetime',
        'total_amount' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'delivery_fee' => 'decimal:2',
    ];

    /**
     * Get the customer that owns the order.
     */
    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Get the promo used for the order.
     */
    public function promo()
    {
        return $this->belongsTo(Promo::class);
    }

    /**
     * Get the items for the order.
     */
    public function items()
    {
        return $this->hasMany(OrderItem::class);
    }

    /**
     * Scope a query to filter orders by status.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  string  $status
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Get the formatted total amount with currency.
     *
     * @return string
     */
    public function getFormattedTotalAttribute()
    {
        return 'Rp ' . number_format($this->total_amount ?? $this->total, 0, ',', '.');
    }

    /**
     * Get the status badge color.
     *
     * @return string
     */
    public function getStatusBadgeAttribute()
    {
        $badges = [
            'pending' => 'warning',
            'confirmed' => 'info',
            'preparing' => 'primary',
            'ready' => 'success',
            'delivered' => 'success',
            'cancelled' => 'danger'
        ];

        return $badges[$this->status] ?? 'secondary';
    }

    /**
     * Get the status text in Indonesian.
     *
     * @return string
     */
    public function getStatusTextAttribute()
    {
        $statuses = [
            'pending' => 'Menunggu Konfirmasi',
            'confirmed' => 'Dikonfirmasi',
            'preparing' => 'Sedang Diproses',
            'ready' => 'Siap Diambil',
            'delivered' => 'Selesai',
            'cancelled' => 'Dibatalkan'
        ];

        return $statuses[$this->status] ?? 'Tidak Diketahui';
    }

    /**
     * Get the payment status text in Indonesian.
     *
     * @return string
     */
    public function getPaymentStatusTextAttribute()
    {
        $statuses = [
            'pending' => 'Belum Dibayar',
            'paid' => 'Sudah Dibayar',
            'failed' => 'Gagal'
        ];

        return $statuses[$this->payment_status] ?? 'Tidak Diketahui';
    }

    /**
     * Generate unique order number.
     *
     * @return string
     */
    public static function generateOrderNumber()
    {
        $prefix = 'AGJ';
        $date = now()->format('ymd');
        $lastOrder = self::whereDate('created_at', now())->count();
        $number = str_pad($lastOrder + 1, 4, '0', STR_PAD_LEFT);
        
        return $prefix . $date . $number;
    }

    /**
     * Calculate total amount including delivery fee and discount.
     *
     * @return float
     */
    public function calculateTotal()
    {
        $itemsTotal = $this->items->sum('subtotal');
        $total = $itemsTotal + $this->delivery_fee - $this->discount_amount;
        
        return max(0, $total); // Ensure total is not negative
    }

    /**
     * Update the total amount based on items.
     */
    public function updateTotal()
    {
        $this->total_amount = $this->calculateTotal();
        $this->save();
    }
}
