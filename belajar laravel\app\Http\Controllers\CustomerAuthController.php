<?php

namespace App\Http\Controllers;

use App\Models\Customer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Session;

class CustomerAuthController extends Controller
{
    /**
     * Show the customer login form.
     */
    public function showLoginForm()
    {
        // Redirect if already logged in
        if (Session::has('customer')) {
            return redirect('/')->with('info', 'Anda sudah login.');
        }
        
        return view('auth.customer-login');
    }

    /**
     * Handle customer login.
     */
    public function login(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'password' => 'required|min:6',
        ]);

        $customer = Customer::where('email', $request->email)->first();

        if ($customer && Hash::check($request->password, $customer->password)) {
            if (!$customer->is_active) {
                return back()->with('error', 'Akun Anda telah dinonaktifkan. Silakan hubungi customer service.');
            }

            // Store customer data in session
            Session::put('customer', [
                'id' => $customer->id,
                'name' => $customer->name,
                'email' => $customer->email,
            ]);

            // Transfer cart items from session to database if any
            $this->transferSessionCartToDatabase($customer->id);

            return redirect()->intended('/')->with('success', 'Selamat datang kembali, ' . $customer->name . '!');
        }

        return back()->with('error', 'Email atau password salah.')->withInput($request->only('email'));
    }

    /**
     * Show the customer registration form.
     */
    public function showRegisterForm()
    {
        // Redirect if already logged in
        if (Session::has('customer')) {
            return redirect('/')->with('info', 'Anda sudah login.');
        }
        
        return view('auth.customer-register');
    }

    /**
     * Handle customer registration.
     */
    public function register(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:customers',
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string',
            'password' => 'required|string|min:6|confirmed',
        ]);

        $customer = Customer::create([
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone,
            'address' => $request->address,
            'password' => Hash::make($request->password),
        ]);

        // Store customer data in session
        Session::put('customer', [
            'id' => $customer->id,
            'name' => $customer->name,
            'email' => $customer->email,
        ]);

        // Transfer cart items from session to database if any
        $this->transferSessionCartToDatabase($customer->id);

        return redirect('/')->with('success', 'Selamat datang di Ayam Goreng Joss, ' . $customer->name . '!');
    }

    /**
     * Handle customer logout.
     */
    public function logout(Request $request)
    {
        // Clear customer session
        Session::forget('customer');
        
        // Also clear any cart session data
        Session::forget('cart');
        
        // Regenerate session ID for security
        $request->session()->regenerate();
        
        return redirect('/')->with('success', 'Anda telah berhasil logout.');
    }

    /**
     * Transfer cart items from session to database when customer logs in.
     */
    private function transferSessionCartToDatabase($customerId)
    {
        $sessionId = Session::getId();

        // Update cart items with customer_id
        \App\Models\Cart::where('session_id', $sessionId)
            ->whereNull('customer_id')
            ->update(['customer_id' => $customerId]);
    }

    /**
     * Get customer profile.
     */
    public function profile()
    {
        if (!Session::has('customer')) {
            return redirect()->route('customer.login')->with('error', 'Silakan login terlebih dahulu.');
        }

        $customer = Customer::find(Session::get('customer.id'));
        
        if (!$customer) {
            Session::forget('customer');
            return redirect()->route('customer.login')->with('error', 'Akun tidak ditemukan. Silakan login kembali.');
        }

        return view('customer.profile', compact('customer'));
    }

    /**
     * Update customer profile.
     */
    public function updateProfile(Request $request)
    {
        if (!Session::has('customer')) {
            return redirect()->route('customer.login')->with('error', 'Silakan login terlebih dahulu.');
        }

        $customer = Customer::find(Session::get('customer.id'));
        
        if (!$customer) {
            Session::forget('customer');
            return redirect()->route('customer.login')->with('error', 'Akun tidak ditemukan. Silakan login kembali.');
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string',
            'password' => 'nullable|string|min:6|confirmed',
        ]);

        $customer->name = $request->name;
        $customer->phone = $request->phone;
        $customer->address = $request->address;

        if ($request->password) {
            $customer->password = Hash::make($request->password);
        }

        $customer->save();

        // Update session data
        Session::put('customer', [
            'id' => $customer->id,
            'name' => $customer->name,
            'email' => $customer->email,
        ]);

        return back()->with('success', 'Profil berhasil diperbarui.');
    }

    /**
     * Get customer order history.
     */
    public function orderHistory()
    {
        if (!Session::has('customer')) {
            return redirect()->route('customer.login')->with('error', 'Silakan login terlebih dahulu.');
        }

        $customer = Customer::find(Session::get('customer.id'));
        
        if (!$customer) {
            Session::forget('customer');
            return redirect()->route('customer.login')->with('error', 'Akun tidak ditemukan. Silakan login kembali.');
        }

        $orders = $customer->orders()->with('items.product')->orderBy('created_at', 'desc')->paginate(10);

        return view('customer.order-history', compact('orders'));
    }
}
