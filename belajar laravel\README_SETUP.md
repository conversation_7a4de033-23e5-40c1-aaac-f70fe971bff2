# Setup Guide - Ayam Goreng Joss Restaurant System

## Deskripsi Sistem
Sistem manajemen restoran Ayam Goreng Joss yang telah diperbaiki dan ditingkatkan dengan fitur-fitur berikut:

### ✅ Fitur yang Telah Diperbaiki:
1. **Sistem Authentication** - Login/Register/Logout untuk customer dan admin
2. **Sistem Cart** - Keranjang belanja yang terintegrasi dengan database
3. **Sistem CRUD** - Create, Read, Update, Delete untuk semua entitas
4. **4 Menu Ayam Goreng Joss** - Menu signature dengan gambar berkualitas
5. **Database Lengkap** - Struktur database yang konsisten dan normalized
6. **Admin Dashboard** - Dashboard admin dengan statistik dan manajemen

### 🍗 Menu Ayam Goreng Joss Signature:
1. **Ayam Goreng Joss Original** - Rp 28.000 (Level Pedas: 1)
2. **Ayam Goreng Joss Pedas Manis** - Rp 32.000 (Level Pedas: 3)
3. **Ayam Goreng Joss Extreme Spicy** - Rp 35.000 (Level Pedas: 5)
4. **Ayam Goreng Joss Keju Leleh** - Rp 38.000 (Level Pedas: 2)

## Persyaratan Sistem
- PHP >= 8.0
- Composer
- MySQL/MariaDB
- XAMPP/WAMP/LAMP
- Node.js (opsional untuk asset compilation)

## Langkah-langkah Setup

### 1. Persiapan Environment
```bash
# Pastikan XAMPP sudah running (Apache + MySQL)
# Buka http://localhost/phpmyadmin
# Buat database baru dengan nama: ayam_goreng_joss
```

### 2. Setup Laravel
```bash
# Masuk ke direktori project
cd "c:\xampp\htdocs\belajar laravel NEW"

# Install dependencies
composer install

# Generate application key
php artisan key:generate

# Setup storage link
php artisan storage:link
```

### 3. Konfigurasi Database
```bash
# Edit file .env (sudah dibuat)
# Pastikan konfigurasi database sesuai:
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=ayam_goreng_joss
DB_USERNAME=root
DB_PASSWORD=
```

### 4. Migrasi dan Seeding Database
```bash
# Jalankan migrasi dan seeding
php artisan migrate:fresh --seed

# Jika ada error, jalankan satu per satu:
php artisan migrate:fresh
php artisan db:seed
```

### 5. Setup Permissions (jika diperlukan)
```bash
# Untuk Windows/XAMPP biasanya tidak perlu
# Untuk Linux/Mac:
chmod -R 775 storage
chmod -R 775 bootstrap/cache
```

## Akun Default

### Admin Accounts:
1. **Super Admin**
   - Email: <EMAIL>
   - Password: password
   - Level: admin

2. **Manager**
   - Email: <EMAIL>
   - Password: password
   - Level: manager

3. **Kasir**
   - Email: <EMAIL>
   - Password: password
   - Level: kasir

### Customer Accounts:
- Email: <EMAIL>
- Password: password123
- (Dan beberapa customer lainnya, lihat CustomerSeeder.php)

## URL Akses

### Frontend (Customer):
- **Homepage**: http://localhost/belajar%20laravel%20NEW/
- **Menu**: http://localhost/belajar%20laravel%20NEW/menu
- **Login Customer**: http://localhost/belajar%20laravel%20NEW/customer/login
- **Register Customer**: http://localhost/belajar%20laravel%20NEW/customer/register
- **Cart**: http://localhost/belajar%20laravel%20NEW/cart

### Backend (Admin):
- **Admin Login**: http://localhost/belajar%20laravel%20NEW/admin/login
- **Admin Dashboard**: http://localhost/belajar%20laravel%20NEW/admin/
- **Manage Products**: http://localhost/belajar%20laravel%20NEW/admin/products
- **Manage Categories**: http://localhost/belajar%20laravel%20NEW/admin/categories
- **Manage Orders**: http://localhost/belajar%20laravel%20NEW/admin/orders
- **Manage Customers**: http://localhost/belajar%20laravel%20NEW/admin/customers

## Fitur-fitur Utama

### 🛒 Customer Features:
1. **Registration & Login** - Daftar dan masuk akun customer
2. **Browse Menu** - Lihat menu dengan filter kategori dan level pedas
3. **Add to Cart** - Tambah produk ke keranjang dengan pilihan level pedas
4. **Checkout** - Proses pemesanan dengan berbagai metode pembayaran
5. **Order History** - Lihat riwayat pesanan
6. **Profile Management** - Kelola profil customer

### 👨‍💼 Admin Features:
1. **Dashboard** - Statistik penjualan dan overview bisnis
2. **Product Management** - CRUD produk dengan upload gambar
3. **Category Management** - CRUD kategori produk
4. **Order Management** - Kelola pesanan dan update status
5. **Customer Management** - Kelola data customer
6. **Reports** - Export data ke CSV

### 🛍️ Cart System:
1. **Session-based Cart** - Cart tersimpan di session untuk guest
2. **Database Cart** - Cart tersimpan di database untuk logged-in user
3. **Cart Transfer** - Otomatis transfer cart dari session ke database saat login
4. **Real-time Updates** - Update quantity dan hapus item secara real-time

## Struktur Database

### Tabel Utama:
1. **users** - Admin users (admin, manager, kasir)
2. **customers** - Customer accounts
3. **categories** - Kategori produk
4. **products** - Produk menu
5. **carts** - Keranjang belanja
6. **orders_new** - Pesanan (tabel baru)
7. **order_items** - Item pesanan
8. **promos** - Kode promo dan diskon
9. **reviews** - Review produk

### Tabel Legacy (untuk kompatibilitas):
- **pelanggans** - Customer lama
- **kategoris** - Kategori lama
- **menus** - Menu lama
- **orders** - Pesanan lama
- **order_details** - Detail pesanan lama

## Troubleshooting

### Error "Class not found":
```bash
composer dump-autoload
```

### Error Database Connection:
1. Pastikan MySQL running di XAMPP
2. Cek konfigurasi .env
3. Pastikan database 'ayam_goreng_joss' sudah dibuat

### Error Permission:
```bash
# Windows
icacls storage /grant Everyone:F /T
icacls bootstrap/cache /grant Everyone:F /T
```

### Error Migration:
```bash
# Reset dan jalankan ulang
php artisan migrate:reset
php artisan migrate
php artisan db:seed
```

### Error Images:
```bash
# Pastikan storage link sudah dibuat
php artisan storage:link

# Buat folder jika belum ada
mkdir storage/app/public/products
mkdir storage/app/public/categories
```

## Testing

### Test Customer Flow:
1. Buka homepage
2. Register customer baru
3. Browse menu dan tambah ke cart
4. Checkout pesanan
5. Lihat order history

### Test Admin Flow:
1. Login sebagai admin
2. Lihat dashboard
3. Tambah produk baru
4. Kelola pesanan customer
5. Export laporan

## Maintenance

### Backup Database:
```bash
# Via phpMyAdmin atau command line
mysqldump -u root -p ayam_goreng_joss > backup.sql
```

### Clear Cache:
```bash
php artisan cache:clear
php artisan config:clear
php artisan view:clear
php artisan route:clear
```

### Update Dependencies:
```bash
composer update
```

## Support

Jika mengalami masalah:
1. Cek log error di `storage/logs/laravel.log`
2. Pastikan semua service XAMPP running
3. Cek konfigurasi .env
4. Jalankan `php artisan config:cache` setelah mengubah .env

## Changelog

### Version 2.0 (Current):
- ✅ Fixed authentication system
- ✅ Improved cart functionality
- ✅ Added 4 signature chicken menus
- ✅ Complete CRUD operations
- ✅ Admin dashboard with statistics
- ✅ Database normalization
- ✅ Image management system
- ✅ Order management system
- ✅ Customer management
- ✅ Responsive design improvements

### Version 1.0 (Legacy):
- Basic menu display
- Simple cart system
- Basic authentication
- Legacy database structure
