@echo off
echo ========================================
echo    AYAM GORENG JOSS - Database Setup
echo ========================================
echo.
echo This will setup the database with sample data
echo Make sure XAMPP MySQL is running!
echo.
pause

echo.
echo Step 1: Generating application key...
php artisan key:generate

echo.
echo Step 2: Creating storage link...
php artisan storage:link

echo.
echo Step 3: Running migrations...
php artisan migrate:fresh

echo.
echo Step 4: Seeding database with sample data...
php artisan db:seed

echo.
echo Step 5: Clearing cache...
php artisan cache:clear
php artisan config:clear
php artisan view:clear

echo.
echo ========================================
echo    DATABASE SETUP COMPLETED!
echo ========================================
echo.
echo Default Admin Accounts:
echo - <EMAIL> / password
echo - <EMAIL> / password  
echo - <EMAIL> / password
echo.
echo Default Customer Account:
echo - <EMAIL> / password123
echo.
echo You can now start the server with start-server.bat
echo ========================================
pause
