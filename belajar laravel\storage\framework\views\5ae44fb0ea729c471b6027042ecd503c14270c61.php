<?php $__env->startSection('title', 'Menu - Ayam Goreng Joss'); ?>

<?php $__env->startSection('content'); ?>
<!-- Menu Header -->
<section class="menu-header py-5" style="background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%); color: white;">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="display-4 fw-bold mb-3" data-aos="fade-up">Menu Ayam Goreng Joss</h1>
                <p class="lead mb-0" data-aos="fade-up" data-aos-delay="100">
                    Pilih level kepedasan favorit Anda dan nikmati kelezatan yang tak terlupakan
                </p>
            </div>
            <div class="col-lg-4 text-end" data-aos="fade-left">
                <i class="bi bi-fire" style="font-size: 5rem; opacity: 0.3;"></i>
            </div>
        </div>
    </div>
</section>

<!-- Menu Filters -->
<section class="menu-filters py-4 bg-light">
    <div class="container">
        <div class="row">
            <!-- Search Bar -->
            <div class="col-lg-4 mb-3">
                <div class="input-group">
                    <input type="text" class="form-control" id="searchInput" placeholder="Cari menu favorit Anda...">
                    <button class="btn btn-outline-secondary" type="button" id="searchBtn">
                        <i class="bi bi-search"></i>
                    </button>
                </div>
            </div>

            <!-- Category Filter -->
            <div class="col-lg-3 mb-3">
                <select class="form-select" id="categoryFilter">
                    <option value="">Semua Kategori</option>
                    <?php $__currentLoopData = $categories ?? $kategoris; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($category->id ?? $category->idkategori); ?>">
                            <?php echo e($category->name ?? $category->kategori); ?>

                        </option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
            </div>

            <!-- Spice Level Filter -->
            <div class="col-lg-3 mb-3">
                <select class="form-select" id="spiceLevelFilter">
                    <option value="">Semua Level Pedas</option>
                    <option value="1">🌶️ Tidak Pedas</option>
                    <option value="2">🌶️🌶️ Sedikit Pedas</option>
                    <option value="3">🌶️🌶️🌶️ Pedas Sedang</option>
                    <option value="4">🌶️🌶️🌶️🌶️ Pedas</option>
                    <option value="5">🌶️🌶️🌶️🌶️🌶️ Sangat Pedas</option>
                </select>
            </div>

            <!-- Price Range -->
            <div class="col-lg-2 mb-3">
                <select class="form-select" id="priceFilter">
                    <option value="">Semua Harga</option>
                    <option value="0-25000">< Rp 25.000</option>
                    <option value="25000-35000">Rp 25.000 - 35.000</option>
                    <option value="35000-50000">Rp 35.000 - 50.000</option>
                    <option value="50000-999999">> Rp 50.000</option>
                </select>
            </div>
        </div>
    </div>
</section>

<!-- Menu Grid -->
<section class="menu-grid py-5">
    <div class="container">
        <div class="row" id="menuContainer">
            <?php if(isset($products) && $products->count() > 0): ?>
                <?php $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="col-lg-4 col-md-6 mb-4 menu-item-wrapper" 
                         data-category="<?php echo e($product->category_id); ?>" 
                         data-name="<?php echo e(strtolower($product->name)); ?>"
                         data-price="<?php echo e($product->price); ?>"
                         data-spice="<?php echo e($product->spice_level); ?>"
                         data-aos="fade-up" 
                         data-aos-delay="<?php echo e(($index % 6 + 1) * 100); ?>">
                        <div class="card menu-item h-100 shadow-sm border-0">
                            <div class="position-relative">
                                <img src="<?php echo e($product->image ? asset('storage/' . $product->image) : asset('images/no-image.jpg')); ?>" 
                                     alt="<?php echo e($product->name); ?>"
                                     class="card-img-top"
                                     style="height: 250px; object-fit: cover;">
                                
                                <!-- Spice Level Badge -->
                                <div class="position-absolute top-0 start-0 m-2">
                                    <span class="badge bg-danger">
                                        <?php for($i = 1; $i <= $product->spice_level; $i++): ?>
                                            🌶️
                                        <?php endfor; ?>
                                    </span>
                                </div>

                                <!-- Premium Badge -->
                                <?php if($product->is_premium): ?>
                                <div class="position-absolute top-0 end-0 m-2">
                                    <span class="badge bg-warning text-dark">
                                        <i class="bi bi-star-fill"></i> Premium
                                    </span>
                                </div>
                                <?php endif; ?>
                            </div>

                            <div class="card-body d-flex flex-column">
                                <h5 class="card-title fw-bold"><?php echo e($product->name); ?></h5>
                                <p class="card-text text-muted small flex-grow-1">
                                    <?php echo e(Str::limit($product->description, 100)); ?>

                                </p>
                                
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <span class="h5 text-primary fw-bold mb-0">
                                        <?php echo e($product->formatted_price ?? 'Rp ' . number_format($product->price, 0, ',', '.')); ?>

                                    </span>
                                    <small class="text-muted">
                                        <?php echo e($product->category->name ?? 'Kategori'); ?>

                                    </small>
                                </div>

                                <!-- Add to Cart Button -->
                                <button class="btn btn-primary btn-add-to-cart" 
                                        data-product-id="<?php echo e($product->id); ?>"
                                        data-product-name="<?php echo e($product->name); ?>"
                                        data-product-price="<?php echo e($product->price); ?>"
                                        data-bs-toggle="modal" 
                                        data-bs-target="#addToCartModal">
                                    <i class="bi bi-cart-plus"></i> Tambah ke Keranjang
                                </button>
                            </div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php elseif(isset($menus) && $menus->count() > 0): ?>
                <?php $__currentLoopData = $menus; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $menu): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="col-lg-4 col-md-6 mb-4 menu-item-wrapper" 
                         data-category="<?php echo e($menu->idkategori); ?>" 
                         data-name="<?php echo e(strtolower($menu->menu)); ?>"
                         data-aos="fade-up" 
                         data-aos-delay="<?php echo e(($index % 6 + 1) * 100); ?>">
                        <div class="card menu-item h-100 shadow-sm border-0">
                            <div class="position-relative">
                                <img src="<?php echo e($menu->gambar ? asset('storage/' . $menu->gambar) : asset('images/no-image.jpg')); ?>" 
                                     alt="<?php echo e($menu->menu); ?>"
                                     class="card-img-top"
                                     style="height: 250px; object-fit: cover;">
                                
                                <!-- Random Spice Level for Legacy -->
                                <div class="position-absolute top-0 start-0 m-2">
                                    <span class="badge bg-danger">
                                        <?php for($i = 1; $i <= rand(1, 5); $i++): ?>
                                            🌶️
                                        <?php endfor; ?>
                                    </span>
                                </div>
                            </div>

                            <div class="card-body d-flex flex-column">
                                <h5 class="card-title fw-bold"><?php echo e($menu->menu); ?></h5>
                                <p class="card-text text-muted small flex-grow-1">
                                    <?php echo e(Str::limit($menu->deskripsi, 100)); ?>

                                </p>
                                
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <span class="h5 text-primary fw-bold mb-0">
                                        Rp <?php echo e(number_format($menu->harga, 0, ',', '.')); ?>

                                    </span>
                                </div>

                                <!-- Legacy Add to Cart Button -->
                                <button class="btn btn-primary btn-add-to-cart-legacy" 
                                        data-menu-id="<?php echo e($menu->idmenu); ?>"
                                        data-menu-name="<?php echo e($menu->menu); ?>"
                                        data-menu-price="<?php echo e($menu->harga); ?>">
                                    <i class="bi bi-cart-plus"></i> Tambah ke Keranjang
                                </button>
                            </div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php else: ?>
                <div class="col-12 text-center py-5">
                    <i class="bi bi-search" style="font-size: 4rem; color: #ccc;"></i>
                    <h3 class="mt-3 text-muted">Menu tidak ditemukan</h3>
                    <p class="text-muted">Coba ubah filter pencarian Anda</p>
                </div>
            <?php endif; ?>
        </div>

        <!-- Pagination -->
        <?php if(isset($products) && method_exists($products, 'links')): ?>
            <div class="d-flex justify-content-center mt-4">
                <?php echo e($products->links()); ?>

            </div>
        <?php elseif(isset($menus) && method_exists($menus, 'links')): ?>
            <div class="d-flex justify-content-center mt-4">
                <?php echo e($menus->links()); ?>

            </div>
        <?php endif; ?>
    </div>
</section>

<!-- Add to Cart Modal -->
<div class="modal fade" id="addToCartModal" tabindex="-1" aria-labelledby="addToCartModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addToCartModalLabel">Tambah ke Keranjang</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="addToCartForm">
                    <?php echo csrf_field(); ?>
                    <input type="hidden" id="productId" name="product_id">
                    
                    <div class="mb-3">
                        <label for="quantity" class="form-label">Jumlah</label>
                        <div class="input-group">
                            <button class="btn btn-outline-secondary" type="button" id="decreaseQty">-</button>
                            <input type="number" class="form-control text-center" id="quantity" name="quantity" value="1" min="1" max="99">
                            <button class="btn btn-outline-secondary" type="button" id="increaseQty">+</button>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="spiceLevel" class="form-label">Level Kepedasan</label>
                        <select class="form-select" id="spiceLevel" name="spice_level" required>
                            <option value="1">🌶️ Tidak Pedas</option>
                            <option value="2">🌶️🌶️ Sedikit Pedas</option>
                            <option value="3" selected>🌶️🌶️🌶️ Pedas Sedang</option>
                            <option value="4">🌶️🌶️🌶️🌶️ Pedas</option>
                            <option value="5">🌶️🌶️🌶️🌶️🌶️ Sangat Pedas</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="specialInstructions" class="form-label">Catatan Khusus (Opsional)</label>
                        <textarea class="form-control" id="specialInstructions" name="special_instructions" rows="3" placeholder="Contoh: Tanpa bawang, extra pedas, dll."></textarea>
                    </div>

                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <strong id="productName"></strong><br>
                            <span class="text-primary h5" id="productPrice"></span>
                        </div>
                        <div>
                            <span class="text-muted">Total: </span>
                            <span class="h5 text-success" id="totalPrice"></span>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                <button type="button" class="btn btn-primary" id="confirmAddToCart">
                    <i class="bi bi-cart-plus"></i> Tambah ke Keranjang
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Cart Notification -->
<div class="position-fixed bottom-0 end-0 p-3" style="z-index: 1050">
    <div id="cartToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header">
            <i class="bi bi-cart-check text-success me-2"></i>
            <strong class="me-auto">Keranjang</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
        <div class="toast-body" id="cartToastBody">
            Produk berhasil ditambahkan ke keranjang!
        </div>
    </div>
</div>

<style>
.menu-item {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.menu-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15) !important;
}

.btn-add-to-cart {
    transition: all 0.3s ease;
}

.btn-add-to-cart:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,123,255,0.3);
}

.menu-filters {
    border-bottom: 1px solid #dee2e6;
}

.badge {
    font-size: 0.75rem;
}

.card-img-top {
    transition: transform 0.3s ease;
}

.menu-item:hover .card-img-top {
    transform: scale(1.05);
}

.input-group .btn {
    border-color: #ced4da;
}

.modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.toast {
    background-color: #fff;
    border: 1px solid #dee2e6;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add to Cart Modal functionality
    const addToCartButtons = document.querySelectorAll('.btn-add-to-cart');
    const modal = document.getElementById('addToCartModal');
    const form = document.getElementById('addToCartForm');
    const quantityInput = document.getElementById('quantity');
    const decreaseBtn = document.getElementById('decreaseQty');
    const increaseBtn = document.getElementById('increaseQty');
    const confirmBtn = document.getElementById('confirmAddToCart');

    // Quantity controls
    decreaseBtn.addEventListener('click', function() {
        const currentValue = parseInt(quantityInput.value);
        if (currentValue > 1) {
            quantityInput.value = currentValue - 1;
            updateTotalPrice();
        }
    });

    increaseBtn.addEventListener('click', function() {
        const currentValue = parseInt(quantityInput.value);
        if (currentValue < 99) {
            quantityInput.value = currentValue + 1;
            updateTotalPrice();
        }
    });

    quantityInput.addEventListener('input', updateTotalPrice);

    // Add to cart button click
    addToCartButtons.forEach(button => {
        button.addEventListener('click', function() {
            const productId = this.dataset.productId;
            const productName = this.dataset.productName;
            const productPrice = this.dataset.productPrice;

            document.getElementById('productId').value = productId;
            document.getElementById('productName').textContent = productName;
            document.getElementById('productPrice').textContent = 'Rp ' + new Intl.NumberFormat('id-ID').format(productPrice);
            
            // Reset form
            quantityInput.value = 1;
            document.getElementById('spiceLevel').value = 3;
            document.getElementById('specialInstructions').value = '';
            
            updateTotalPrice();
        });
    });

    // Update total price
    function updateTotalPrice() {
        const quantity = parseInt(quantityInput.value) || 1;
        const priceText = document.getElementById('productPrice').textContent;
        const price = parseInt(priceText.replace(/[^\d]/g, ''));
        const total = quantity * price;
        
        document.getElementById('totalPrice').textContent = 'Rp ' + new Intl.NumberFormat('id-ID').format(total);
    }

    // Confirm add to cart
    confirmBtn.addEventListener('click', function() {
        const formData = new FormData(form);
        
        fetch('<?php echo e(route("cart.add")); ?>', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Close modal
                const modalInstance = bootstrap.Modal.getInstance(modal);
                modalInstance.hide();
                
                // Show toast
                showToast(data.message);
                
                // Update cart count
                updateCartCount(data.cart_count);
            } else {
                alert(data.message || 'Terjadi kesalahan');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Terjadi kesalahan saat menambahkan ke keranjang');
        });
    });

    // Show toast notification
    function showToast(message) {
        const toastBody = document.getElementById('cartToastBody');
        const toast = document.getElementById('cartToast');
        
        toastBody.textContent = message;
        
        const toastInstance = new bootstrap.Toast(toast);
        toastInstance.show();
    }

    // Update cart count in navbar
    function updateCartCount(count) {
        const cartBadge = document.querySelector('.cart-count');
        if (cartBadge) {
            cartBadge.textContent = count;
            cartBadge.style.display = count > 0 ? 'inline' : 'none';
        }
    }

    // Filter functionality
    const searchInput = document.getElementById('searchInput');
    const categoryFilter = document.getElementById('categoryFilter');
    const spiceLevelFilter = document.getElementById('spiceLevelFilter');
    const priceFilter = document.getElementById('priceFilter');
    const menuItems = document.querySelectorAll('.menu-item-wrapper');

    function filterMenus() {
        const searchTerm = searchInput.value.toLowerCase();
        const selectedCategory = categoryFilter.value;
        const selectedSpiceLevel = spiceLevelFilter.value;
        const selectedPriceRange = priceFilter.value;

        menuItems.forEach(item => {
            const name = item.dataset.name;
            const category = item.dataset.category;
            const spiceLevel = item.dataset.spice;
            const price = parseInt(item.dataset.price) || 0;

            let show = true;

            // Search filter
            if (searchTerm && !name.includes(searchTerm)) {
                show = false;
            }

            // Category filter
            if (selectedCategory && category !== selectedCategory) {
                show = false;
            }

            // Spice level filter
            if (selectedSpiceLevel && spiceLevel !== selectedSpiceLevel) {
                show = false;
            }

            // Price filter
            if (selectedPriceRange) {
                const [minPrice, maxPrice] = selectedPriceRange.split('-').map(Number);
                if (price < minPrice || price > maxPrice) {
                    show = false;
                }
            }

            item.style.display = show ? 'block' : 'none';
        });
    }

    // Add event listeners for filters
    searchInput.addEventListener('input', filterMenus);
    categoryFilter.addEventListener('change', filterMenus);
    spiceLevelFilter.addEventListener('change', filterMenus);
    priceFilter.addEventListener('change', filterMenus);

    // Legacy cart functionality for old menu system
    const legacyButtons = document.querySelectorAll('.btn-add-to-cart-legacy');
    legacyButtons.forEach(button => {
        button.addEventListener('click', function() {
            alert('Fitur ini akan segera tersedia. Silakan gunakan menu baru di atas.');
        });
    });
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\belajar laravel NEW\Ayamweb\belajar laravel\resources\views/menu.blade.php ENDPATH**/ ?>