<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('order_items', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('order_id');
            $table->unsignedBigInteger('product_id');
            $table->integer('quantity');
            $table->decimal('price', 10, 2);
            $table->integer('spice_level')->default(1);
            $table->text('special_instructions')->nullable();

            // Keep original fields for backward compatibility
            $table->integer('idorderdetail')->nullable();
            $table->string('idorder')->nullable();
            $table->integer('idmenu')->nullable();
            $table->integer('jumlah')->nullable();
            $table->float('hargajual')->nullable();

            $table->timestamps();

            // Foreign keys will be added after all tables are created
            $table->index(['order_id']);
            $table->index(['product_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('order_items');
    }
};
