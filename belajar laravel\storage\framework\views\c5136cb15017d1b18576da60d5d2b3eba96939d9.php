<?php $__env->startSection('title', 'Keranjang Belanja - Ayam Gore<PERSON> Jo<PERSON>'); ?>

<?php $__env->startSection('content'); ?>
<!-- Cart <PERSON>er -->
<section class="cart-header py-4" style="background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%); color: white;">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="h2 fw-bold mb-2" data-aos="fade-up">
                    <i class="bi bi-cart3 me-2"></i>Keranjang Belanja
                </h1>
                <p class="mb-0" data-aos="fade-up" data-aos-delay="100">
                    Review pesanan Anda sebelum melanjutkan ke checkout
                </p>
            </div>
            <div class="col-lg-4 text-end" data-aos="fade-left">
                <a href="<?php echo e(route('menu')); ?>" class="btn btn-light">
                    <i class="bi bi-arrow-left me-2"></i>Lanjut Belanja
                </a>
            </div>
        </div>
    </div>
</section>

<div class="container py-5">
    <?php if($cartItems && $cartItems->count() > 0): ?>
        <div class="row">
            <!-- Cart Items -->
            <div class="col-lg-8 mb-4">
                <div class="card shadow-sm border-0" data-aos="fade-up">
                    <div class="card-header bg-white py-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0 fw-bold">Item Pesanan (<?php echo e($cartItems->count()); ?>)</h5>
                            <button class="btn btn-outline-danger btn-sm" id="clearCartBtn">
                                <i class="bi bi-trash me-1"></i>Kosongkan Keranjang
                            </button>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <?php $__currentLoopData = $cartItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="cart-item border-bottom p-4" data-item-id="<?php echo e($item->id); ?>">
                                <div class="row align-items-center">
                                    <!-- Product Image -->
                                    <div class="col-md-2 col-3 mb-3 mb-md-0">
                                        <img src="<?php echo e($item->product->image ? asset('storage/' . $item->product->image) : asset('images/no-image.jpg')); ?>" 
                                             alt="<?php echo e($item->product->name); ?>"
                                             class="img-fluid rounded"
                                             style="height: 80px; width: 80px; object-fit: cover;">
                                    </div>

                                    <!-- Product Info -->
                                    <div class="col-md-4 col-9 mb-3 mb-md-0">
                                        <h6 class="fw-bold mb-1"><?php echo e($item->product->name); ?></h6>
                                        <p class="text-muted small mb-1"><?php echo e($item->product->category->name ?? 'Kategori'); ?></p>
                                        
                                        <!-- Spice Level -->
                                        <div class="mb-1">
                                            <span class="badge bg-danger">
                                                <?php for($i = 1; $i <= $item->spice_level; $i++): ?>
                                                    🌶️
                                                <?php endfor; ?>
                                                <?php echo e($item->spice_level_description); ?>

                                            </span>
                                        </div>

                                        <!-- Special Instructions -->
                                        <?php if($item->special_instructions): ?>
                                            <small class="text-info">
                                                <i class="bi bi-chat-square-text me-1"></i>
                                                <?php echo e($item->special_instructions); ?>

                                            </small>
                                        <?php endif; ?>
                                    </div>

                                    <!-- Price -->
                                    <div class="col-md-2 col-6 mb-3 mb-md-0 text-center">
                                        <div class="fw-bold text-primary"><?php echo e($item->formatted_price); ?></div>
                                    </div>

                                    <!-- Quantity Controls -->
                                    <div class="col-md-3 col-6 mb-3 mb-md-0">
                                        <div class="d-flex align-items-center justify-content-center">
                                            <button class="btn btn-outline-secondary btn-sm quantity-btn" 
                                                    data-action="decrease" 
                                                    data-item-id="<?php echo e($item->id); ?>">
                                                <i class="bi bi-dash"></i>
                                            </button>
                                            <input type="number" 
                                                   class="form-control form-control-sm text-center mx-2 quantity-input" 
                                                   value="<?php echo e($item->quantity); ?>" 
                                                   min="1" 
                                                   max="99"
                                                   style="width: 60px;"
                                                   data-item-id="<?php echo e($item->id); ?>">
                                            <button class="btn btn-outline-secondary btn-sm quantity-btn" 
                                                    data-action="increase" 
                                                    data-item-id="<?php echo e($item->id); ?>">
                                                <i class="bi bi-plus"></i>
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Remove Button -->
                                    <div class="col-md-1 col-12 text-center">
                                        <button class="btn btn-outline-danger btn-sm remove-item-btn" 
                                                data-item-id="<?php echo e($item->id); ?>">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </div>

                                <!-- Subtotal -->
                                <div class="row mt-2">
                                    <div class="col-12 text-end">
                                        <span class="text-muted">Subtotal: </span>
                                        <span class="fw-bold text-success item-subtotal"><?php echo e($item->formatted_subtotal); ?></span>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </div>

            <!-- Order Summary -->
            <div class="col-lg-4">
                <div class="card shadow-sm border-0 sticky-top" style="top: 100px;" data-aos="fade-up" data-aos-delay="200">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0 fw-bold">
                            <i class="bi bi-receipt me-2"></i>Ringkasan Pesanan
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- Items Summary -->
                        <div class="d-flex justify-content-between mb-2">
                            <span>Subtotal (<?php echo e($cartItems->sum('quantity')); ?> item)</span>
                            <span id="cartSubtotal"><?php echo e('Rp ' . number_format($total, 0, ',', '.')); ?></span>
                        </div>

                        <!-- Delivery Fee -->
                        <div class="d-flex justify-content-between mb-2">
                            <span>Biaya Pengiriman</span>
                            <span class="text-success" id="deliveryFee">Gratis</span>
                        </div>

                        <!-- Discount -->
                        <div class="d-flex justify-content-between mb-3">
                            <span>Diskon</span>
                            <span class="text-success" id="discount">-</span>
                        </div>

                        <hr>

                        <!-- Total -->
                        <div class="d-flex justify-content-between mb-4">
                            <h5 class="fw-bold">Total</h5>
                            <h5 class="fw-bold text-primary" id="cartTotal"><?php echo e('Rp ' . number_format($total, 0, ',', '.')); ?></h5>
                        </div>

                        <!-- Promo Code -->
                        <div class="mb-3">
                            <label for="promoCode" class="form-label small">Kode Promo</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="promoCode" placeholder="Masukkan kode">
                                <button class="btn btn-outline-secondary" type="button" id="applyPromoBtn">
                                    Gunakan
                                </button>
                            </div>
                        </div>

                        <!-- Checkout Button -->
                        <?php if(Session::has('customer')): ?>
                            <button class="btn btn-primary btn-lg w-100 mb-3" id="checkoutBtn">
                                <i class="bi bi-credit-card me-2"></i>Lanjut ke Pembayaran
                            </button>
                        <?php else: ?>
                            <div class="text-center mb-3">
                                <p class="small text-muted mb-2">Silakan login untuk melanjutkan</p>
                                <a href="<?php echo e(route('customer.login')); ?>" class="btn btn-primary btn-lg w-100">
                                    <i class="bi bi-box-arrow-in-right me-2"></i>Login untuk Checkout
                                </a>
                            </div>
                        <?php endif; ?>

                        <!-- Continue Shopping -->
                        <a href="<?php echo e(route('menu')); ?>" class="btn btn-outline-primary w-100">
                            <i class="bi bi-arrow-left me-2"></i>Lanjut Belanja
                        </a>
                    </div>
                </div>

                <!-- Delivery Info -->
                <div class="card shadow-sm border-0 mt-3" data-aos="fade-up" data-aos-delay="300">
                    <div class="card-body text-center">
                        <i class="bi bi-truck text-primary mb-2" style="font-size: 2rem;"></i>
                        <h6 class="fw-bold">Gratis Ongkir</h6>
                        <p class="small text-muted mb-0">
                            Untuk pembelian minimal Rp 50.000 dalam radius 5km
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Checkout Modal -->
        <div class="modal fade" id="checkoutModal" tabindex="-1" aria-labelledby="checkoutModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="checkoutModalLabel">
                            <i class="bi bi-credit-card me-2"></i>Checkout Pesanan
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <form action="<?php echo e(route('cart.checkout')); ?>" method="POST" id="checkoutForm">
                        <?php echo csrf_field(); ?>
                        <div class="modal-body">
                            <!-- Payment Method -->
                            <div class="mb-4">
                                <label class="form-label fw-bold">Metode Pembayaran</label>
                                <div class="row">
                                    <div class="col-md-6 mb-2">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="payment_method" id="cash" value="cash" checked>
                                            <label class="form-check-label" for="cash">
                                                <i class="bi bi-cash me-2"></i>Tunai
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="payment_method" id="transfer" value="transfer">
                                            <label class="form-check-label" for="transfer">
                                                <i class="bi bi-bank me-2"></i>Transfer Bank
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="payment_method" id="ewallet" value="ewallet">
                                            <label class="form-check-label" for="ewallet">
                                                <i class="bi bi-phone me-2"></i>E-Wallet
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="payment_method" id="card" value="card">
                                            <label class="form-check-label" for="card">
                                                <i class="bi bi-credit-card me-2"></i>Kartu Kredit
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Delivery Address -->
                            <div class="mb-3">
                                <label for="delivery_address" class="form-label fw-bold">Alamat Pengiriman (Opsional)</label>
                                <textarea class="form-control" id="delivery_address" name="delivery_address" rows="3" 
                                          placeholder="Masukkan alamat lengkap jika ingin diantar"></textarea>
                                <div class="form-text">Kosongkan jika ingin ambil sendiri (take away)</div>
                            </div>

                            <!-- Notes -->
                            <div class="mb-3">
                                <label for="notes" class="form-label fw-bold">Catatan Pesanan (Opsional)</label>
                                <textarea class="form-control" id="notes" name="notes" rows="2" 
                                          placeholder="Catatan khusus untuk pesanan Anda"></textarea>
                            </div>

                            <!-- Order Summary in Modal -->
                            <div class="bg-light p-3 rounded">
                                <h6 class="fw-bold mb-2">Ringkasan Pesanan</h6>
                                <div class="d-flex justify-content-between">
                                    <span>Total Pembayaran:</span>
                                    <span class="fw-bold text-primary" id="modalTotal"><?php echo e('Rp ' . number_format($total, 0, ',', '.')); ?></span>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                            <button type="submit" class="btn btn-primary" id="confirmCheckoutBtn">
                                <i class="bi bi-check-circle me-2"></i>Konfirmasi Pesanan
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

    <?php else: ?>
        <!-- Empty Cart -->
        <div class="row justify-content-center">
            <div class="col-md-6 text-center" data-aos="fade-up">
                <div class="card shadow-sm border-0">
                    <div class="card-body py-5">
                        <i class="bi bi-cart-x text-muted mb-3" style="font-size: 4rem;"></i>
                        <h3 class="fw-bold mb-3">Keranjang Kosong</h3>
                        <p class="text-muted mb-4">
                            Belum ada item di keranjang Anda. Yuk, pilih menu favorit dan mulai pesan!
                        </p>
                        <a href="<?php echo e(route('menu')); ?>" class="btn btn-primary btn-lg">
                            <i class="bi bi-list-ul me-2"></i>Lihat Menu
                        </a>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<style>
.cart-item {
    transition: all 0.3s ease;
}

.cart-item:hover {
    background-color: #f8f9fa;
}

.quantity-input {
    border: 1px solid #dee2e6;
}

.quantity-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(255, 107, 53, 0.25);
}

.sticky-top {
    position: sticky;
    top: 100px;
    z-index: 1020;
}

.btn-outline-danger:hover {
    background-color: #dc3545;
    border-color: #dc3545;
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

@media (max-width: 768px) {
    .sticky-top {
        position: relative;
        top: auto;
    }
    
    .cart-item .row > div {
        margin-bottom: 1rem;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Quantity controls
    document.querySelectorAll('.quantity-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const action = this.dataset.action;
            const itemId = this.dataset.itemId;
            const input = document.querySelector(`.quantity-input[data-item-id="${itemId}"]`);
            
            let newQuantity = parseInt(input.value);
            
            if (action === 'increase' && newQuantity < 99) {
                newQuantity++;
            } else if (action === 'decrease' && newQuantity > 1) {
                newQuantity--;
            }
            
            input.value = newQuantity;
            updateCartItem(itemId, newQuantity);
        });
    });

    // Quantity input change
    document.querySelectorAll('.quantity-input').forEach(input => {
        input.addEventListener('change', function() {
            const itemId = this.dataset.itemId;
            const quantity = parseInt(this.value);
            
            if (quantity >= 1 && quantity <= 99) {
                updateCartItem(itemId, quantity);
            } else {
                this.value = 1;
                updateCartItem(itemId, 1);
            }
        });
    });

    // Remove item
    document.querySelectorAll('.remove-item-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const itemId = this.dataset.itemId;
            
            if (confirm('Yakin ingin menghapus item ini dari keranjang?')) {
                removeCartItem(itemId);
            }
        });
    });

    // Clear cart
    const clearCartBtn = document.getElementById('clearCartBtn');
    if (clearCartBtn) {
        clearCartBtn.addEventListener('click', function() {
            if (confirm('Yakin ingin mengosongkan seluruh keranjang?')) {
                clearCart();
            }
        });
    }

    // Checkout button
    const checkoutBtn = document.getElementById('checkoutBtn');
    if (checkoutBtn) {
        checkoutBtn.addEventListener('click', function() {
            const modal = new bootstrap.Modal(document.getElementById('checkoutModal'));
            modal.show();
        });
    }

    // Checkout form submission
    const checkoutForm = document.getElementById('checkoutForm');
    if (checkoutForm) {
        checkoutForm.addEventListener('submit', function() {
            const confirmBtn = document.getElementById('confirmCheckoutBtn');
            const originalText = confirmBtn.innerHTML;
            
            confirmBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Memproses...';
            confirmBtn.disabled = true;
        });
    }

    // Apply promo code
    const applyPromoBtn = document.getElementById('applyPromoBtn');
    if (applyPromoBtn) {
        applyPromoBtn.addEventListener('click', function() {
            const promoCode = document.getElementById('promoCode').value;
            
            if (promoCode.trim()) {
                // TODO: Implement promo code functionality
                alert('Fitur kode promo akan segera tersedia!');
            }
        });
    }

    // Update cart item quantity
    function updateCartItem(itemId, quantity) {
        const formData = new FormData();
        formData.append('quantity', quantity);
        formData.append('_method', 'PUT');
        
        fetch(`/cart/${itemId}`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update subtotal for this item
                const itemRow = document.querySelector(`[data-item-id="${itemId}"]`);
                const subtotalElement = itemRow.querySelector('.item-subtotal');
                subtotalElement.textContent = data.subtotal;
                
                // Update cart totals
                updateCartTotals(data.cart_total);
                
                // Show success message
                showToast(data.message, 'success');
            } else {
                showToast(data.message || 'Terjadi kesalahan', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('Terjadi kesalahan saat memperbarui keranjang', 'error');
        });
    }

    // Remove cart item
    function removeCartItem(itemId) {
        fetch(`/cart/${itemId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Remove item from DOM
                const itemRow = document.querySelector(`[data-item-id="${itemId}"]`);
                itemRow.remove();
                
                // Update cart totals
                updateCartTotals(data.cart_total);
                
                // Update cart count in navbar
                updateCartCount(data.cart_count);
                
                // Show success message
                showToast(data.message, 'success');
                
                // Reload page if cart is empty
                if (data.cart_count === 0) {
                    setTimeout(() => {
                        location.reload();
                    }, 1000);
                }
            } else {
                showToast(data.message || 'Terjadi kesalahan', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('Terjadi kesalahan saat menghapus item', 'error');
        });
    }

    // Clear entire cart
    function clearCart() {
        fetch('/cart/clear', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast(data.message, 'success');
                setTimeout(() => {
                    location.reload();
                }, 1000);
            } else {
                showToast(data.message || 'Terjadi kesalahan', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('Terjadi kesalahan saat mengosongkan keranjang', 'error');
        });
    }

    // Update cart totals
    function updateCartTotals(newTotal) {
        const cartSubtotal = document.getElementById('cartSubtotal');
        const cartTotal = document.getElementById('cartTotal');
        const modalTotal = document.getElementById('modalTotal');
        
        if (cartSubtotal) cartSubtotal.textContent = newTotal;
        if (cartTotal) cartTotal.textContent = newTotal;
        if (modalTotal) modalTotal.textContent = newTotal;
    }

    // Update cart count in navbar
    function updateCartCount(count) {
        const cartBadge = document.querySelector('.cart-count');
        if (cartBadge) {
            cartBadge.textContent = count;
            cartBadge.style.display = count > 0 ? 'flex' : 'none';
        }
    }

    // Show toast notification
    function showToast(message, type = 'success') {
        // Create toast element
        const toastHtml = `
            <div class="toast align-items-center text-white bg-${type === 'success' ? 'success' : 'danger'} border-0" role="alert">
                <div class="d-flex">
                    <div class="toast-body">
                        <i class="bi bi-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            </div>
        `;
        
        // Add to toast container
        let toastContainer = document.querySelector('.toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
            toastContainer.style.zIndex = '1055';
            document.body.appendChild(toastContainer);
        }
        
        toastContainer.insertAdjacentHTML('beforeend', toastHtml);
        
        // Show toast
        const toastElement = toastContainer.lastElementChild;
        const toast = new bootstrap.Toast(toastElement);
        toast.show();
        
        // Remove toast element after it's hidden
        toastElement.addEventListener('hidden.bs.toast', () => {
            toastElement.remove();
        });
    }
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\belajar laravel NEW\Ayamweb\belajar laravel\resources\views/cart/index.blade.php ENDPATH**/ ?>