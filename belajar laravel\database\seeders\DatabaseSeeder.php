<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     *
     * @return void
     */
    public function run()
    {
        // Create admin user
        \App\Models\User::factory()->create([
            'name' => 'Admin',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'level' => 'admin',
        ]);

        // Create additional admin users
        \App\Models\User::factory()->create([
            'name' => 'Manager',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'level' => 'manager',
        ]);

        \App\Models\User::factory()->create([
            'name' => 'Kasir',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'level' => 'kasir',
        ]);

        // Call other seeders
        $this->call([
            CategorySeeder::class,
            ProductSeeder::class,
            CustomerSeeder::class,
            PromoSeeder::class,
            ChickenImageSeeder::class,
        ]);
    }
}
