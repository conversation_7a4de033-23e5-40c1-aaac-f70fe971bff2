<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Add foreign keys for carts table
        if (Schema::hasTable('carts') && Schema::hasTable('customers') && Schema::hasTable('products')) {
            Schema::table('carts', function (Blueprint $table) {
                if (!$this->foreignKeyExists('carts', 'carts_customer_id_foreign')) {
                    $table->foreign('customer_id')->references('id')->on('customers')->onDelete('cascade');
                }
                if (!$this->foreignKeyExists('carts', 'carts_product_id_foreign')) {
                    $table->foreign('product_id')->references('id')->on('products')->onDelete('cascade');
                }
            });
        }

        // Add foreign keys for orders_new table
        if (Schema::hasTable('orders_new') && Schema::hasTable('customers') && Schema::hasTable('promos')) {
            Schema::table('orders_new', function (Blueprint $table) {
                if (!$this->foreignKeyExists('orders_new', 'orders_new_customer_id_foreign')) {
                    $table->foreign('customer_id')->references('id')->on('customers')->onDelete('cascade');
                }
                if (!$this->foreignKeyExists('orders_new', 'orders_new_promo_id_foreign')) {
                    $table->foreign('promo_id')->references('id')->on('promos')->onDelete('set null');
                }
            });
        }

        // Add foreign keys for order_items table
        if (Schema::hasTable('order_items') && Schema::hasTable('orders_new') && Schema::hasTable('products')) {
            Schema::table('order_items', function (Blueprint $table) {
                if (!$this->foreignKeyExists('order_items', 'order_items_order_id_foreign')) {
                    $table->foreign('order_id')->references('id')->on('orders_new')->onDelete('cascade');
                }
                if (!$this->foreignKeyExists('order_items', 'order_items_product_id_foreign')) {
                    $table->foreign('product_id')->references('id')->on('products')->onDelete('cascade');
                }
            });
        }

        // Add foreign keys for reviews table
        if (Schema::hasTable('reviews') && Schema::hasTable('customers') && Schema::hasTable('products') && Schema::hasTable('orders_new')) {
            Schema::table('reviews', function (Blueprint $table) {
                if (!$this->foreignKeyExists('reviews', 'reviews_customer_id_foreign')) {
                    $table->foreign('customer_id')->references('id')->on('customers')->onDelete('cascade');
                }
                if (!$this->foreignKeyExists('reviews', 'reviews_product_id_foreign')) {
                    $table->foreign('product_id')->references('id')->on('products')->onDelete('cascade');
                }
                if (!$this->foreignKeyExists('reviews', 'reviews_order_id_foreign')) {
                    $table->foreign('order_id')->references('id')->on('orders_new')->onDelete('set null');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Drop foreign keys in reverse order
        if (Schema::hasTable('reviews')) {
            Schema::table('reviews', function (Blueprint $table) {
                $table->dropForeign(['customer_id']);
                $table->dropForeign(['product_id']);
                $table->dropForeign(['order_id']);
            });
        }

        if (Schema::hasTable('order_items')) {
            Schema::table('order_items', function (Blueprint $table) {
                $table->dropForeign(['order_id']);
                $table->dropForeign(['product_id']);
            });
        }

        if (Schema::hasTable('orders_new')) {
            Schema::table('orders_new', function (Blueprint $table) {
                $table->dropForeign(['customer_id']);
                $table->dropForeign(['promo_id']);
            });
        }

        if (Schema::hasTable('carts')) {
            Schema::table('carts', function (Blueprint $table) {
                $table->dropForeign(['customer_id']);
                $table->dropForeign(['product_id']);
            });
        }
    }

    /**
     * Check if foreign key exists
     */
    private function foreignKeyExists($table, $foreignKey)
    {
        $foreignKeys = Schema::getConnection()
            ->getDoctrineSchemaManager()
            ->listTableForeignKeys($table);
        
        foreach ($foreignKeys as $key) {
            if ($key->getName() === $foreignKey) {
                return true;
            }
        }
        
        return false;
    }
};
