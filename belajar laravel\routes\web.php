<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\CustomerAuthController;
use App\Http\Controllers\CartController;
use App\Http\Controllers\FrontController;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\Admin\ProductController;
use App\Http\Controllers\Admin\OrderController;
use App\Http\Controllers\Admin\CategoryController;
use App\Http\Controllers\Admin\CustomerController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
*/

// Frontend Routes
Route::get('/', [FrontController::class, 'home'])->name('home');
Route::get('/menu', [FrontController::class, 'menu'])->name('menu');
Route::get('/kontak', [FrontController::class, 'kontak'])->name('kontak');
Route::get('/product/{id}', [FrontController::class, 'productDetail'])->name('product.detail');
Route::get('/search-products', [FrontController::class, 'searchProducts'])->name('search.products');
Route::get('/category/{id}/products', [FrontController::class, 'getProductsByCategory'])->name('category.products');

// Customer Authentication Routes
Route::prefix('customer')->name('customer.')->group(function () {
    Route::get('/login', [CustomerAuthController::class, 'showLoginForm'])->name('login');
    Route::post('/login', [CustomerAuthController::class, 'login']);
    Route::get('/register', [CustomerAuthController::class, 'showRegisterForm'])->name('register');
    Route::post('/register', [CustomerAuthController::class, 'register']);
    Route::post('/logout', [CustomerAuthController::class, 'logout'])->name('logout');

    // Customer profile and order history
    Route::get('/profile', [CustomerAuthController::class, 'profile'])->name('profile');
    Route::post('/profile', [CustomerAuthController::class, 'updateProfile'])->name('profile.update');
    Route::get('/orders', [CustomerAuthController::class, 'orderHistory'])->name('order-history');
});

// Cart Routes
Route::prefix('cart')->name('cart.')->group(function () {
    Route::get('/', [CartController::class, 'index'])->name('index');
    Route::post('/add', [CartController::class, 'add'])->name('add');
    Route::put('/{id}', [CartController::class, 'update'])->name('update');
    Route::delete('/{id}', [CartController::class, 'remove'])->name('remove');
    Route::post('/clear', [CartController::class, 'clear'])->name('clear');
    Route::get('/count', [CartController::class, 'getCount'])->name('count');
    Route::post('/checkout', [CartController::class, 'checkout'])->name('checkout');
});

// Admin Routes
Route::prefix('admin')->name('admin.')->middleware(['auth', 'CekLogin'])->group(function () {
    Route::get('/', [OrderController::class, 'dashboard'])->name('dashboard');

    // Products Management
    Route::resource('products', ProductController::class);
    Route::patch('/products/{product}/toggle-active', [ProductController::class, 'toggleActive'])->name('products.toggle-active');

    // Categories Management
    Route::resource('categories', CategoryController::class);
    Route::patch('/categories/{category}/toggle-active', [CategoryController::class, 'toggleActive'])->name('categories.toggle-active');

    // Orders Management
    Route::resource('orders', OrderController::class)->only(['index', 'show']);
    Route::patch('/orders/{order}/status', [OrderController::class, 'updateStatus'])->name('orders.update-status');
    Route::patch('/orders/{order}/payment', [OrderController::class, 'updatePaymentStatus'])->name('orders.update-payment');
    Route::patch('/orders/{order}/cancel', [OrderController::class, 'cancel'])->name('orders.cancel');
    Route::get('/orders/{order}/receipt', [OrderController::class, 'printReceipt'])->name('orders.receipt');
    Route::get('/orders-export', [OrderController::class, 'export'])->name('orders.export');

    // Customers Management
    Route::resource('customers', CustomerController::class);
    Route::patch('/customers/{customer}/toggle-active', [CustomerController::class, 'toggleActive'])->name('customers.toggle-active');
});

// Admin Authentication
Route::prefix('admin')->group(function () {
    Route::get('/login', [AuthController::class, 'index'])->name('admin.login');
    Route::post('/postlogin', [AuthController::class, 'postlogin'])->name('admin.postlogin');
    Route::get('/logout', [AuthController::class, 'logout'])->name('admin.logout');
});

// Legacy routes for backward compatibility
Route::get('/order', function () { return redirect('/cart'); });
Route::get('/chat', function () { return redirect('/kontak'); });
