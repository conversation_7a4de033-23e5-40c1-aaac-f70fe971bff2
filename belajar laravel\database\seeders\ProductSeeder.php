<?php

namespace Database\Seeders;

use App\Models\Product;
use Illuminate\Database\Seeder;

class ProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $products = [
            // Ayam Goreng Joss Signature Menu
            [
                'category_id' => 1, // Ayam Original
                'name' => 'Ayam Goreng Joss Original',
                'description' => 'Ayam goreng crispy dengan bumbu rahasia Joss yang gurih dan renyah. Digoreng dengan teknik khusus untuk menghasilkan tekstur crispy di luar dan juicy di dalam. Cocok untuk yang tidak terlalu suka pedas.',
                'price' => 28000,
                'image' => 'products/ayam-joss-original.jpg',
                'spice_level' => 1,
                'is_premium' => false,
                'is_active' => true,
            ],
            [
                'category_id' => 1, // Ayam Original
                'name' => 'Ayam Goreng Joss Pedas Manis',
                'description' => 'Perpaduan sempurna antara pedas dan manis dengan bumbu khas Joss. Ayam goreng crispy yang dibalut saus pedas manis yang menggugah selera. Level kepedasan yang pas untuk semua kalangan.',
                'price' => 32000,
                'image' => 'products/ayam-joss-pedas-manis.jpg',
                'spice_level' => 3,
                'is_premium' => false,
                'is_active' => true,
            ],
            [
                'category_id' => 2, // Ayam Premium
                'name' => 'Ayam Goreng Joss Extreme Spicy',
                'description' => 'Menu andalan untuk pecinta pedas ekstrem! Ayam goreng crispy dengan bumbu cabai rawit super pedas dan rempah pilihan. Dijamin bikin ketagihan dan berkeringat. Tantangan untuk yang berani!',
                'price' => 35000,
                'image' => 'products/ayam-joss-extreme.jpg',
                'spice_level' => 5,
                'is_premium' => true,
                'is_active' => true,
            ],
            [
                'category_id' => 2, // Ayam Premium
                'name' => 'Ayam Goreng Joss Keju Leleh',
                'description' => 'Inovasi terbaru! Ayam goreng crispy yang dilapisi keju mozzarella leleh yang creamy. Kombinasi gurih ayam dengan kelembutan keju yang meleleh di mulut. Menu premium yang wajib dicoba!',
                'price' => 38000,
                'image' => 'products/ayam-joss-keju.jpg',
                'spice_level' => 2,
                'is_premium' => true,
                'is_active' => true,
            ],
            
            // Menu Tambahan
            [
                'category_id' => 1,
                'name' => 'Ayam Goreng Bumbu Bali',
                'description' => 'Ayam goreng dengan bumbu khas Bali yang kaya rempah. Perpaduan bumbu base genep yang autentik memberikan cita rasa yang unik dan menggugah selera.',
                'price' => 30000,
                'image' => 'products/ayam-bumbu-bali.jpg',
                'spice_level' => 3,
                'is_premium' => false,
                'is_active' => true,
            ],
            [
                'category_id' => 1,
                'name' => 'Ayam Goreng Kremes',
                'description' => 'Ayam goreng dengan taburan kremes renyah yang melimpah. Tekstur crispy yang berlapis-lapis memberikan sensasi makan yang berbeda dan memuaskan.',
                'price' => 29000,
                'image' => 'products/ayam-kremes.jpg',
                'spice_level' => 2,
                'is_premium' => false,
                'is_active' => true,
            ],
            [
                'category_id' => 2,
                'name' => 'Ayam Goreng Sambal Matah',
                'description' => 'Ayam goreng crispy dengan sambal matah khas Bali yang segar. Kombinasi pedas, asam, dan aroma serai yang menggugah selera. Menu premium dengan cita rasa yang eksotis.',
                'price' => 36000,
                'image' => 'products/ayam-sambal-matah.jpg',
                'spice_level' => 4,
                'is_premium' => true,
                'is_active' => true,
            ],
            [
                'category_id' => 2,
                'name' => 'Ayam Goreng Black Pepper',
                'description' => 'Ayam goreng dengan bumbu lada hitam yang aromatic dan pedas. Cita rasa western dengan sentuhan Indonesia yang memberikan pengalaman kuliner yang berbeda.',
                'price' => 34000,
                'image' => 'products/ayam-black-pepper.jpg',
                'spice_level' => 3,
                'is_premium' => true,
                'is_active' => true,
            ],

            // Side Dishes
            [
                'category_id' => 3, // Sides
                'name' => 'Nasi Putih',
                'description' => 'Nasi putih pulen yang hangat, cocok untuk menemani ayam goreng favorit Anda.',
                'price' => 5000,
                'image' => 'products/nasi-putih.jpg',
                'spice_level' => 1,
                'is_premium' => false,
                'is_active' => true,
            ],
            [
                'category_id' => 3,
                'name' => 'French Fries',
                'description' => 'Kentang goreng crispy yang renyah di luar dan lembut di dalam. Cocok sebagai pelengkap atau camilan.',
                'price' => 15000,
                'image' => 'products/french-fries.jpg',
                'spice_level' => 1,
                'is_premium' => false,
                'is_active' => true,
            ],
            [
                'category_id' => 3,
                'name' => 'Lalapan Segar',
                'description' => 'Sayuran segar pilihan: timun, tomat, kemangi, dan kol. Dilengkapi dengan sambal terasi yang pedas.',
                'price' => 8000,
                'image' => 'products/lalapan.jpg',
                'spice_level' => 3,
                'is_premium' => false,
                'is_active' => true,
            ],
            [
                'category_id' => 3,
                'name' => 'Tahu Tempe Goreng',
                'description' => 'Tahu dan tempe goreng crispy yang gurih. Sumber protein nabati yang sehat dan lezat.',
                'price' => 10000,
                'image' => 'products/tahu-tempe.jpg',
                'spice_level' => 1,
                'is_premium' => false,
                'is_active' => true,
            ],

            // Beverages
            [
                'category_id' => 4, // Minuman
                'name' => 'Es Teh Manis',
                'description' => 'Teh manis dingin yang menyegarkan. Cocok untuk menetralkan rasa pedas dari ayam goreng.',
                'price' => 8000,
                'image' => 'products/es-teh-manis.jpg',
                'spice_level' => 1,
                'is_premium' => false,
                'is_active' => true,
            ],
            [
                'category_id' => 4,
                'name' => 'Es Jeruk Nipis',
                'description' => 'Minuman segar dari perasan jeruk nipis asli dengan es batu. Sangat menyegarkan dan membantu meredakan pedas.',
                'price' => 10000,
                'image' => 'products/es-jeruk-nipis.jpg',
                'spice_level' => 1,
                'is_premium' => false,
                'is_active' => true,
            ],
            [
                'category_id' => 4,
                'name' => 'Es Kelapa Muda',
                'description' => 'Air kelapa muda segar langsung dari buahnya. Minuman alami yang menyehatkan dan menyegarkan.',
                'price' => 15000,
                'image' => 'products/es-kelapa-muda.jpg',
                'spice_level' => 1,
                'is_premium' => false,
                'is_active' => true,
            ],
            [
                'category_id' => 4,
                'name' => 'Jus Alpukat',
                'description' => 'Jus alpukat creamy dengan susu kental manis. Minuman bergizi tinggi yang mengenyangkan.',
                'price' => 18000,
                'image' => 'products/jus-alpukat.jpg',
                'spice_level' => 1,
                'is_premium' => true,
                'is_active' => true,
            ],
        ];

        foreach ($products as $product) {
            Product::create($product);
        }
    }
}
