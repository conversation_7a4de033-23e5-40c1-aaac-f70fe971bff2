<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('orders_new', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('customer_id');
            $table->string('order_number')->unique();
            $table->datetime('order_date');
            $table->decimal('total_amount', 10, 2);
            $table->enum('status', ['pending', 'confirmed', 'preparing', 'ready', 'delivered', 'cancelled'])->default('pending');
            $table->enum('payment_method', ['cash', 'transfer', 'ewallet', 'card'])->default('cash');
            $table->enum('payment_status', ['pending', 'paid', 'failed'])->default('pending');
            $table->unsignedBigInteger('promo_id')->nullable();
            $table->decimal('discount_amount', 10, 2)->default(0);
            $table->text('delivery_address')->nullable();
            $table->decimal('delivery_fee', 10, 2)->default(0);
            $table->text('notes')->nullable();

            // Keep original fields for backward compatibility
            $table->string('idorder')->nullable();
            $table->integer('idpelanggan')->nullable();
            $table->date('tglorder')->nullable();
            $table->float('total')->nullable();
            $table->float('bayar')->nullable();
            $table->float('kembali')->nullable();

            $table->timestamps();

            // Foreign keys will be added after all tables are created
            $table->index(['customer_id']);
            $table->index(['promo_id']);
            $table->index(['order_date', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('orders_new');
    }
};
