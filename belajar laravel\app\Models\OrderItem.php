<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OrderItem extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'order_id',
        'product_id',
        'quantity',
        'price',
        'spice_level',
        'special_instructions',
        // Keep original fields for backward compatibility
        'idorderdetail',
        'idorder',
        'idmenu',
        'jumlah',
        'hargajual',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'price' => 'decimal:2',
        'quantity' => 'integer',
        'spice_level' => 'integer',
    ];

    /**
     * Get the order that owns the order item.
     */
    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Get the product that owns the order item.
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Get the subtotal for this order item.
     *
     * @return float
     */
    public function getSubtotalAttribute()
    {
        return $this->quantity * $this->price;
    }

    /**
     * Get the formatted subtotal with currency.
     *
     * @return string
     */
    public function getFormattedSubtotalAttribute()
    {
        return 'Rp ' . number_format($this->subtotal, 0, ',', '.');
    }

    /**
     * Get the formatted price with currency.
     *
     * @return string
     */
    public function getFormattedPriceAttribute()
    {
        return 'Rp ' . number_format($this->price, 0, ',', '.');
    }

    /**
     * Get spice level description.
     *
     * @return string
     */
    public function getSpiceLevelDescriptionAttribute()
    {
        $levels = [
            1 => 'Tidak Pedas',
            2 => 'Sedikit Pedas',
            3 => 'Pedas',
            4 => 'Sangat Pedas',
            5 => 'Extra Pedas'
        ];

        return $levels[$this->spice_level] ?? 'Tidak Pedas';
    }
}
