<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\DB;
use App\Models\Category;
use App\Models\Product;
use App\Models\Customer;
use App\Models\Promo;

try {
    echo "Starting database fix...\n";

    // Insert categories
    $categories = [
        ['name' => 'Ayam Original', 'description' => 'Ayam goreng original dengan berbagai level kepedasan', 'is_active' => true],
        ['name' => 'Ayam Premium', 'description' => 'Ayam goreng premium dengan bumbu spesial', 'is_active' => true],
        ['name' => 'Sides', 'description' => 'Makanan pendamping', 'is_active' => true],
        ['name' => 'Minuman', 'description' => 'Minuman segar', 'is_active' => true],
    ];

    foreach ($categories as $category) {
        Category::firstOrCreate(['name' => $category['name']], $category);
    }
    echo "Categories inserted successfully.\n";

    // Insert products
    $products = [
        [
            'category_id' => 1,
            'name' => 'Ayam Goreng Original',
            'description' => 'Ayam goreng renyah dengan bumbu original yang gurih, cocok untuk yang tidak terlalu suka pedas.',
            'price' => 25000,
            'spice_level' => 1,
            'is_premium' => false,
            'is_active' => true,
        ],
        [
            'category_id' => 1,
            'name' => 'Ayam Goreng Medium Spicy',
            'description' => 'Ayam goreng dengan tingkat kepedasan sedang, cocok untuk penikmat pedas pemula.',
            'price' => 28000,
            'spice_level' => 3,
            'is_premium' => false,
            'is_active' => true,
        ],
        [
            'category_id' => 1,
            'name' => 'Ayam Goreng Extreme Spicy',
            'description' => 'Ayam goreng dengan tingkat kepedasan ekstrim, tantangan bagi pecinta pedas sejati!',
            'price' => 35000,
            'spice_level' => 5,
            'is_premium' => true,
            'is_active' => true,
        ],
        [
            'category_id' => 2,
            'name' => 'Ayam Cheese Explosion',
            'description' => 'Ayam goreng dengan saus keju meleleh yang creamy dan lezat, sedikit pedas.',
            'price' => 32000,
            'spice_level' => 2,
            'is_premium' => false,
            'is_active' => true,
        ],
        [
            'category_id' => 2,
            'name' => 'Ayam BBQ Spicy',
            'description' => 'Ayam goreng dengan saus BBQ pedas yang smoky dan menggugah selera.',
            'price' => 30000,
            'spice_level' => 3,
            'is_premium' => false,
            'is_active' => true,
        ],
        [
            'category_id' => 2,
            'name' => 'Ayam Sambal Matah',
            'description' => 'Ayam goreng dengan sambal matah khas Bali yang segar dan pedas.',
            'price' => 33000,
            'spice_level' => 4,
            'is_premium' => true,
            'is_active' => true,
        ],
        [
            'category_id' => 3,
            'name' => 'French Fries',
            'description' => 'Kentang goreng renyah',
            'price' => 15000,
            'spice_level' => 1,
            'is_premium' => false,
            'is_active' => true,
        ],
        [
            'category_id' => 3,
            'name' => 'Cheese Balls',
            'description' => 'Bola-bola keju yang lezat',
            'price' => 18000,
            'spice_level' => 1,
            'is_premium' => false,
            'is_active' => true,
        ],
        [
            'category_id' => 4,
            'name' => 'Es Teh Manis',
            'description' => 'Teh manis dingin yang menyegarkan',
            'price' => 8000,
            'spice_level' => 1,
            'is_premium' => false,
            'is_active' => true,
        ],
        [
            'category_id' => 4,
            'name' => 'Es Jeruk',
            'description' => 'Jeruk segar dengan es',
            'price' => 10000,
            'spice_level' => 1,
            'is_premium' => false,
            'is_active' => true,
        ],
    ];

    foreach ($products as $product) {
        Product::firstOrCreate(['name' => $product['name']], $product);
    }
    echo "Products inserted successfully.\n";

    // Insert demo customer
    Customer::firstOrCreate(
        ['email' => '<EMAIL>'],
        [
            'name' => 'Budi Santoso',
            'email' => '<EMAIL>',
            'phone' => '081234567890',
            'address' => 'Jl. Contoh No. 123, Jakarta',
            'password' => bcrypt('password123'),
            'is_active' => true,
        ]
    );
    echo "Demo customer created successfully.\n";

    // Insert promos
    $promos = [
        [
            'code' => 'WELCOME10',
            'description' => 'Diskon 10% untuk pelanggan baru',
            'discount_percent' => 10.00,
            'min_order_amount' => 50000,
            'start_date' => '2024-01-01',
            'end_date' => '2024-12-31',
            'is_active' => true,
        ],
        [
            'code' => 'JOSSEXTREME',
            'description' => 'Beli 2 Ayam Goreng Extreme Spicy, Gratis 1 Minuman Segar',
            'discount_percent' => 10.00,
            'min_order_amount' => 70000,
            'start_date' => '2024-01-01',
            'end_date' => '2024-12-31',
            'is_active' => true,
        ],
    ];

    foreach ($promos as $promo) {
        Promo::firstOrCreate(['code' => $promo['code']], $promo);
    }
    echo "Promos inserted successfully.\n";

    echo "Database fix completed successfully!\n";

} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
